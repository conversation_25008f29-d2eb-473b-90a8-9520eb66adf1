"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Upload,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  FileCheck,
} from "lucide-react";
import { toast } from "sonner";

interface UploadState {
  stage: "initial" | "uploading" | "success" | "error";
  pdfFile: File | null;
  extractedData: {
    document_name?: string;
    applicant_name?: string;
    document_data?: Record<string, string>;
    generation_info?: {
      system?: string;
    };
  } | null;
  error: string;
  success: string;
}

export default function UploadPage() {
  const [state, setState] = useState<UploadState>({
    stage: "initial",
    pdfFile: null,
    extractedData: null,
    error: "",
    success: "",
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        setState((prev) => ({
          ...prev,
          error: "Please select a PDF file",
          success: "",
        }));
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error("File size must be less than 10MB");
        setState((prev) => ({
          ...prev,
          error: "File size must be less than 10MB",
          success: "",
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        pdfFile: file,
        error: "",
        success: "",
      }));
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!state.pdfFile) {
      toast.error("Please select a PDF file");
      return;
    }

    setState((prev) => ({
      ...prev,
      stage: "uploading",
      error: "",
      success: "",
    }));

    try {
      const formData = new FormData();
      formData.append("pdfFile", state.pdfFile);

      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        toast.success("Document uploaded and processed successfully!");
        setState((prev) => ({
          ...prev,
          stage: "success",
          extractedData: result.extractedData,
          success: result.message,
          error: "",
        }));

        // Reset form after 3 seconds
        setTimeout(() => {
          setState({
            stage: "initial",
            pdfFile: null,
            extractedData: null,
            error: "",
            success: "",
          });
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }, 3000);
      } else {
        toast.error(result.error || "Upload failed");
        setState((prev) => ({
          ...prev,
          stage: "error",
          error: result.error || "Upload failed",
          success: "",
        }));
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Network error. Please try again.");
      setState((prev) => ({
        ...prev,
        stage: "error",
        error: "Network error. Please try again.",
        success: "",
      }));
    }
  };

  const resetForm = () => {
    setState({
      stage: "initial",
      pdfFile: null,
      extractedData: null,
      error: "",
      success: "",
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Document Upload</h1>
          <p className="text-muted-foreground">
            Upload generated PDF documents to extract embedded data and store in
            the system.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              PDF Document Upload
            </CardTitle>
            <CardDescription>
              Upload a PDF document generated by the LDIS system. The embedded
              metadata will be extracted and stored in the documents database.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* PDF File Upload */}
              <div className="space-y-2">
                <Label htmlFor="pdfFile" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  PDF Document *
                </Label>
                <Input
                  id="pdfFile"
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  disabled={state.stage === "uploading"}
                  className="cursor-pointer"
                />
                {state.pdfFile && (
                  <p className="text-sm text-muted-foreground">
                    Selected: {state.pdfFile.name} (
                    {(state.pdfFile.size / 1024 / 1024).toFixed(2)} MB)
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  Upload a PDF document generated by the LDIS system. Max file
                  size: 10MB.
                </p>
              </div>

              {/* Error Alert */}
              {state.error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{state.error}</AlertDescription>
                </Alert>
              )}

              {/* Success Alert */}
              {state.success && (
                <Alert className="border-green-200 bg-green-50 text-green-800">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{state.success}</AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <div className="flex gap-4">
                <Button
                  type="submit"
                  disabled={!state.pdfFile || state.stage === "uploading"}
                  className="flex-1"
                >
                  {state.stage === "uploading" ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload & Process
                    </>
                  )}
                </Button>

                {(state.stage === "error" || state.stage === "success") && (
                  <Button type="button" variant="outline" onClick={resetForm}>
                    Upload Another
                  </Button>
                )}
              </div>
            </form>

            {/* Extracted Data Preview */}
            {state.extractedData && state.stage === "success" && (
              <div className="mt-6 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <FileCheck className="h-4 w-4" />
                  Extracted Document Data
                </h3>
                <div className="space-y-2 text-sm">
                  {state.extractedData.document_name && (
                    <div className="flex items-center gap-2">
                      <FileText className="h-3 w-3" />
                      <span className="font-medium">Document:</span>
                      <span>{state.extractedData.document_name}</span>
                    </div>
                  )}
                  {state.extractedData.applicant_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-3 w-3" />
                      <span className="font-medium">Applicant:</span>
                      <span>{state.extractedData.applicant_name}</span>
                    </div>
                  )}
                  {state.extractedData.generation_info?.system && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3" />
                      <span className="font-medium">Generated by:</span>
                      <span>{state.extractedData.generation_info.system}</span>
                    </div>
                  )}
                  {state.extractedData.document_data &&
                    Object.keys(state.extractedData.document_data).length >
                      0 && (
                      <div>
                        <span className="font-medium">Form Data:</span>
                        <div className="ml-4 mt-1 space-y-1">
                          {Object.entries(
                            state.extractedData.document_data
                          ).map(([key, value]) => (
                            <div key={key} className="text-xs">
                              <span className="font-medium">{key}:</span>{" "}
                              {value}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
